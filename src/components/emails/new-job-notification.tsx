import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewJobNotificationProps {
  jobName: string;
  jobId: string;
  tradeName: string;
  budget: number;
  location: string;
  deadline: string;
  userId: string;
  recipientEmail: string;
}

const NewJobNotificationEmail = ({
  jobName,
  jobId,
  tradeName,
  budget,
  location,
  deadline,
  userId,
  recipientEmail,
}: NewJobNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        Jack here - found a great {tradeName} opportunity for you!
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                New Job Opportunity! 🔨
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - I found a {tradeName} job that might be perfect for
                you!
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Budget:</strong> ${budget}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Location:</strong> {location}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>Deadline:</strong> {deadline}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Based on your {tradeName} expertise and location, this looks
                like a great match! The homeowner is looking for quality work,
                and I think you'd be perfect for this project.
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  View Job & Submit Bid
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Pro tip: Quick responses often win more bids! Take a look at the
                details and submit your proposal while it's fresh.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Need help crafting the perfect bid? Just ask me - I'm here to
                help you succeed!
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default NewJobNotificationEmail;
