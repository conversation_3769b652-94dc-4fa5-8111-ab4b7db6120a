import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface EmailVerificationProps {
  verificationCode: string;
  verificationLink: string;
  userId?: string;
  recipientEmail?: string;
}

const EmailVerificationEmail = ({
  verificationCode,
  verificationLink,
  userId,
  recipientEmail,
}: EmailVerificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>Hey there! Jack here - let's verify your email address</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Hey there! 👋
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here from TradeCrews - let's get your email verified!
              </Text>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Welcome to TradeCrews! I'm excited to help you with your home
                improvement projects. To get started, I just need to verify your
                email address - it's a quick security step to keep your account
                safe.
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[12px] font-medium text-[16px] text-gray-700">
                  Your verification code:
                </Text>
                <Text className="mb-[8px] text-center font-bold text-[24px] text-gray-800">
                  {verificationCode}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Simply click the button below to verify your email and we'll get
                you connected with skilled professionals for your next project!
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={verificationLink}
                >
                  Verify My Email
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                If you didn't create this account, no worries - you can safely
                ignore this email.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Looking forward to helping you with your home projects!
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                {userId && recipientEmail ? (
                  <a
                    href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                    className="text-gray-500 underline"
                  >
                    Unsubscribe
                  </a>
                ) : null}
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default EmailVerificationEmail;
