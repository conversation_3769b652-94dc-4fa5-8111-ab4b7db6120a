import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface BidDeadlineNotificationProps {
  jobName: string;
  jobId: string;
  tradeName: string;
  deadline: string;
  daysRemaining: string;
  location: string;
  budget: number;
  userId: string;
  recipientEmail: string;
}

const BidDeadlineNotificationEmail = ({
  jobName,
  jobId,
  tradeName,
  deadline,
  daysRemaining,
  location,
  budget,
  userId,
  recipientEmail,
}: BidDeadlineNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        Jack here - friendly reminder: {daysRemaining} days left to bid!
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Friendly Reminder ⏰
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - just {daysRemaining} days left to bid on this great{" "}
                {tradeName} opportunity!
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-orange-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Budget:</strong> ${budget}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Location:</strong> {location}
                </Text>
                <Text className="mb-[8px] font-medium text-[16px] text-red-600">
                  <strong>Deadline:</strong> {deadline} ({daysRemaining} days
                  left)
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                I wanted to give you a heads up that this opportunity won't be
                available much longer. The homeowner is actively reviewing bids,
                and I'd hate for you to miss out on what looks like a great
                project match!
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  Submit My Bid Now
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Remember, quality bids submitted early often have the best
                chance of success. Take a few minutes to put together a
                thoughtful proposal - it could be your next great project!
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Need help crafting a winning bid? I'm here to assist with
                pricing strategies and proposal tips.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default BidDeadlineNotificationEmail;
