import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewBidNotificationProps {
  bidName: string;
  bidId: string;
  jobName: string;
  contractorName: string;
  amount: number;
  estimatedDuration: number;
  userId: string;
  recipientEmail: string;
}

const NewBidNotificationEmail = ({
  bidName,
  bidId,
  jobName,
  contractorName,
  amount,
  estimatedDuration,
  userId,
  recipientEmail,
}: NewBidNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        Great news! Jack here - you've got a new bid on your project
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Great News! New Bid 🎉
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - you've got a new bid on your project!
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {bidName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Project:</strong> {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Contractor:</strong> {contractorName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Bid Amount:</strong> ${amount}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>Estimated Duration:</strong> {estimatedDuration} days
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                A qualified contractor has reviewed your project and submitted
                their proposal. This is exciting progress! Let me help you
                evaluate this bid alongside any others you receive.
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/bids/${bidId}`}
                >
                  Review This Bid
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Take your time to review the details, check their credentials,
                and compare with other bids. You're in control - accept when you
                find the right fit!
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Need help evaluating bids or have questions about the
                contractor? I'm here to help you make the best decision for your
                project.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default NewBidNotificationEmail;
