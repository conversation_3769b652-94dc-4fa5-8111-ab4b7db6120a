import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface TradeCrewsLaunchEmailProps {
  userId?: string;
  recipientEmail?: string;
}

const TradeCrewsLaunchEmail = ({
  userId,
  recipientEmail,
}: TradeCrewsLaunchEmailProps = {}) => {
  return (
    <Html>
      <Head />
      <Preview>
        Jack here - welcome to TradeCrews! Let me show you around
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Welcome to <span className="text-blue-600">TradeCrews</span>! 👋
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - I'm excited to help you with your home improvement
                journey!
              </Text>

              <Text className="mb-[20px] text-[16px] text-gray-600">
                I'm Jack, your AI assistant here at TradeCrews! I'm here to make
                your home improvement projects as smooth and successful as
                possible. Think of me as your personal guide through every step
                of the process.
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[12px] font-medium text-[16px] text-gray-700">
                  Here's how I can help you:
                </Text>
                <ul className="m-0 p-0 pl-[20px]">
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Find the perfect professionals</strong> - I'll match
                    you with verified experts who have the right skills for your
                    project
                  </li>
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Help you compare bids</strong> - I'll guide you
                    through evaluating quotes and choosing the best option
                  </li>
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Answer your questions</strong> - From project
                    planning to troubleshooting, I'm here 24/7
                  </li>
                  <li className="text-[16px] text-gray-600">
                    <strong>Keep everything organized</strong> - I'll help you
                    manage your projects from start to finish
                  </li>
                </ul>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Whether you need a plumber, electrician, carpenter, or any other
                professional, I'm here to connect you with trusted experts who
                can get the job done right. No more guessing or hoping - just
                quality results!
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href="https://tradecrews.com"
                >
                  Let's Get Started!
                </Button>
              </Section>

              <Section className="mb-[24px]">
                <Text className="mb-[16px] text-[16px] text-gray-600">
                  Are you a skilled tradesperson? I'd love to help you grow your
                  business too! TradeCrews gives you a platform to showcase your
                  expertise, build your reputation, and connect with homeowners
                  who need your skills.
                </Text>

                <Text className="mb-[16px] text-[16px] text-gray-600">
                  I'll help match you with the right projects and support you
                  throughout the process. Join our community of professionals
                  and let's build something great together!
                </Text>
              </Section>

              <Section className="text-center">
                <Button
                  className="mb-[24px] box-border rounded-[4px] border border-blue-600 bg-white px-[24px] py-[12px] text-center font-bold text-[16px] text-blue-600 no-underline"
                  href="https://tradecrews.com/professionals"
                >
                  Join as a Professional
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Ready to get started? I'm here whenever you need me - just look
                for the chat button and say hello! I'm excited to help make your
                home improvement dreams a reality.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="mt-[16px] border-gray-200 border-t pt-[20px]">
              <Text className="mb-[8px] text-center text-[14px] text-gray-600">
                Have questions? Just chat with me directly on the platform, or
                reach out at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 no-underline"
                >
                  <EMAIL>
                </a>
              </Text>

              <Text className="m-0 text-center text-[12px] text-gray-500">
                TradeCrews Inc., 123 Main Street, Suite 100, Anytown, AN 12345
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={
                    userId && recipientEmail
                      ? `https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`
                      : "https://tradecrews.com/unsubscribe"
                  }
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default TradeCrewsLaunchEmail;
