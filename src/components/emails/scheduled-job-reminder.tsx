import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface ScheduledJobReminderProps {
  jobName: string;
  jobId: string;
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  recipientRole: "HOMEOWNER" | "PROFESSIONAL";
  counterpartyName: string;
  userId: string;
  recipientEmail: string;
}

const ScheduledJobReminderEmail = ({
  jobName,
  jobId,
  scheduledDate,
  scheduledTime,
  location,
  recipientRole,
  counterpartyName,
  userId,
  recipientEmail,
}: ScheduledJobReminderProps) => {
  const isHomeowner = recipientRole === "HOMEOWNER";

  return (
    <Html>
      <Head />
      <Preview>Jack here - friendly reminder about your upcoming job!</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Job Reminder 📅
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - just a friendly reminder about your upcoming job!
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Date:</strong> {scheduledDate}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Time:</strong> {scheduledTime}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Location:</strong> {location}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>{isHomeowner ? "Contractor" : "Client"}:</strong>{" "}
                  {counterpartyName}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Everything's on track for your project! Your contractor will arrive at the scheduled time. Just make sure the property is accessible and you're ready to go."
                  : "You're all set for this job! Please arrive at the scheduled time and contact the homeowner if you need any access instructions or have questions."}
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  View Job Details
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Need to reschedule? No problem - just reach out to your contractor as soon as possible to coordinate a new time."
                  : "If something comes up and you need to reschedule, please contact the homeowner right away to work out a new time that works for both of you."}
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "I'm here if you have any questions about the project or need help communicating with your contractor!"
                  : "Questions about the job or need help with anything? I'm here to support you throughout the project!"}
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default ScheduledJobReminderEmail;
