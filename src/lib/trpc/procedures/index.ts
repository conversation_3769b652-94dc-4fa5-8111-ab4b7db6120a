import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import type { Context } from "../core/context";

// Legacy imports for backward compatibility
import {
  bidMiddlewares,
  createPermissionMiddleware,
  jobMiddlewares,
  messageMiddlewares,
  organizationMiddlewares,
  propertyMiddlewares,
  requireAdmin,
  requireContractor,
  requireHomeowner,
  requireOrganizationAdmin,
  requireOrganizationOwner,
  requireOrganizationRole,
  requireRole,
  reviewMiddlewares,
  scheduleMiddlewares,
  templateMiddlewares,
} from "../middleware/permissions";
import type {
  OrganizationRole,
  PermissionAction,
  ResourceType,
  UserRole,
} from "../types/permissions";

// ============================================================================
// TRPC INITIALIZATION
// ============================================================================

const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// BASE MIDDLEWARES
// ============================================================================

/**
 * Authentication middleware
 */
const isAuthed = t.middleware(({ next, ctx }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

// ============================================================================
// BASE PROCEDURES
// ============================================================================

// Export better-auth procedures as primary exports
export { protectedProcedure, publicProcedure, router } from "./better-auth";

// Legacy exports for backward compatibility
export const legacyRouter = t.router;
export const legacyPublicProcedure = t.procedure;
export const legacyProtectedProcedure = t.procedure.use(isAuthed);

// ============================================================================
// ROLE-BASED PROCEDURES
// ============================================================================

// Export better-auth role procedures as primary exports
export {
  adminProcedure,
  contractorProcedure,
  homeownerProcedure,
} from "./better-auth";

// Legacy role procedures for backward compatibility
export const legacyAdminProcedure = legacyProtectedProcedure.use(requireAdmin);
export const legacyContractorProcedure =
  legacyProtectedProcedure.use(requireContractor);
export const legacyHomeownerProcedure =
  legacyProtectedProcedure.use(requireHomeowner);

// ============================================================================
// ORGANIZATION ROLE PROCEDURES
// ============================================================================

export const organizationOwnerProcedure = protectedProcedure.use(
  requireOrganizationOwner,
);
export const organizationAdminProcedure = protectedProcedure.use(
  requireOrganizationAdmin,
);

// ============================================================================
// PERMISSION-BASED PROCEDURE BUILDERS
// ============================================================================

/**
 * Create a procedure with specific permission requirements
 */
export function createPermissionProcedure(
  resource: ResourceType,
  action: PermissionAction,
  options?: {
    requireOwnership?: boolean;
    allowSameOrganization?: boolean;
    customCheck?: (ctx: any) => Promise<boolean> | boolean;
  },
) {
  return protectedProcedure.use(
    createPermissionMiddleware({
      resource,
      action,
      ...options,
    }),
  );
}

/**
 * Create a procedure that requires specific user role(s)
 */
export function createRoleProcedure(requiredRole: UserRole | UserRole[]) {
  return protectedProcedure.use(requireRole(requiredRole));
}

/**
 * Create a procedure that requires specific organization role(s)
 */
export function createOrganizationRoleProcedure(
  requiredRole: OrganizationRole | OrganizationRole[],
) {
  return protectedProcedure.use(requireOrganizationRole(requiredRole));
}

// ============================================================================
// RESOURCE-SPECIFIC PROCEDURES
// ============================================================================

/**
 * Job-related procedures with better-auth permissions
 */
export { jobProcedures } from "./better-auth";

/**
 * Legacy job-related procedures
 */
export const legacyJobProcedures = {
  create: legacyProtectedProcedure.use(jobMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(jobMiddlewares.canRead),
  update: legacyProtectedProcedure.use(jobMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(jobMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(jobMiddlewares.canList),
  manage: legacyProtectedProcedure.use(jobMiddlewares.canManage),
};

/**
 * Bid-related procedures with better-auth permissions
 */
export { bidProcedures } from "./better-auth";

/**
 * Legacy bid-related procedures
 */
export const legacyBidProcedures = {
  create: legacyProtectedProcedure.use(bidMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(bidMiddlewares.canRead),
  update: legacyProtectedProcedure.use(bidMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(bidMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(bidMiddlewares.canList),
  approve: legacyProtectedProcedure.use(bidMiddlewares.canApprove),
  reject: legacyProtectedProcedure.use(bidMiddlewares.canReject),
};

/**
 * Property-related procedures
 */
export const propertyProcedures = {
  create: protectedProcedure.use(propertyMiddlewares.canCreate),
  read: protectedProcedure.use(propertyMiddlewares.canRead),
  update: protectedProcedure.use(propertyMiddlewares.canUpdate),
  delete: protectedProcedure.use(propertyMiddlewares.canDelete),
  list: protectedProcedure.use(propertyMiddlewares.canList),
};

/**
 * Organization-related procedures
 */
export const organizationProcedures = {
  create: protectedProcedure.use(organizationMiddlewares.canCreate),
  read: protectedProcedure.use(organizationMiddlewares.canRead),
  update: protectedProcedure.use(organizationMiddlewares.canUpdate),
  delete: protectedProcedure.use(organizationMiddlewares.canDelete),
  list: protectedProcedure.use(organizationMiddlewares.canList),
  manage: protectedProcedure.use(organizationMiddlewares.canManage),
  invite: protectedProcedure.use(organizationMiddlewares.canInvite),
};

/**
 * Message-related procedures
 */
export const messageProcedures = {
  create: protectedProcedure.use(messageMiddlewares.canCreate),
  read: protectedProcedure.use(messageMiddlewares.canRead),
  list: protectedProcedure.use(messageMiddlewares.canList),
};

/**
 * Review-related procedures
 */
export const reviewProcedures = {
  create: protectedProcedure.use(reviewMiddlewares.canCreate),
  read: protectedProcedure.use(reviewMiddlewares.canRead),
  list: protectedProcedure.use(reviewMiddlewares.canList),
};

/**
 * Schedule-related procedures
 */
export const scheduleProcedures = {
  create: protectedProcedure.use(scheduleMiddlewares.canCreate),
  read: protectedProcedure.use(scheduleMiddlewares.canRead),
  update: protectedProcedure.use(scheduleMiddlewares.canUpdate),
  list: protectedProcedure.use(scheduleMiddlewares.canList),
  approve: protectedProcedure.use(scheduleMiddlewares.canApprove),
};

/**
 * Template-related procedures
 */
export const templateProcedures = {
  create: protectedProcedure.use(templateMiddlewares.canCreate),
  read: protectedProcedure.use(templateMiddlewares.canRead),
  update: protectedProcedure.use(templateMiddlewares.canUpdate),
  delete: protectedProcedure.use(templateMiddlewares.canDelete),
  list: protectedProcedure.use(templateMiddlewares.canList),
};

export const tradeProcedures = {
  create: adminProcedure,
  read: protectedProcedure,
  update: adminProcedure,
  delete: adminProcedure,
  list: protectedProcedure,
};

// ============================================================================
// UTILITY PROCEDURES
// ============================================================================

/**
 * Create a CRUD procedure set for a resource
 */
export function createCRUDProcedures(resource: ResourceType) {
  return {
    create: createPermissionProcedure(resource, "create"),
    read: createPermissionProcedure(resource, "read"),
    update: createPermissionProcedure(resource, "update", {
      requireOwnership: true,
    }),
    delete: createPermissionProcedure(resource, "delete", {
      requireOwnership: true,
    }),
    list: createPermissionProcedure(resource, "list"),
  };
}

/**
 * Create a read-only procedure set for a resource
 */
export function createReadOnlyProcedures(resource: ResourceType) {
  return {
    read: createPermissionProcedure(resource, "read"),
    list: createPermissionProcedure(resource, "list"),
  };
}

/**
 * Create an admin-only procedure set for a resource
 */
export function createAdminProcedures() {
  return {
    create: adminProcedure,
    read: adminProcedure,
    update: adminProcedure,
    delete: adminProcedure,
    list: adminProcedure,
    manage: adminProcedure,
  };
}

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================

/**
 * Legacy admin procedure (for backward compatibility)
 */
export const adminProcedureLegacy = adminProcedure;

/**
 * Legacy protected procedure (for backward compatibility)
 */
export const protectedProcedureLegacy = protectedProcedure;
