import { initTRPC } from "@trpc/server";
import superjson from "superjson";
import type { Context } from "../core/context";
import {
  betterAuthMiddleware,
  bidPermissions,
  createBetterAuthPermission,
  jobPermissions,
  messagePermissions,
  organizationPermissions,
  propertyPermissions,
  requireAdmin,
  requireContractor,
  requireHomeowner,
  reviewPermissions,
  schedulePermissions,
  servicePermissions,
  templatePermissions,
} from "../middleware/better-auth-permissions";

// ============================================================================
// TRPC INITIALIZATION
// ============================================================================

const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// BASE PROCEDURES WITH BETTER-AUTH
// ============================================================================

export const router = t.router;
export const publicProcedure = t.procedure;
export const protectedProcedure = t.procedure.use(betterAuthMiddleware);

// ============================================================================
// ROLE-BASED PROCEDURES
// ============================================================================

export const adminProcedure = protectedProcedure.use(requireAdmin);
export const contractorProcedure = protectedProcedure.use(requireContractor);
export const homeownerProcedure = protectedProcedure.use(requireHomeowner);

// ============================================================================
// RESOURCE-SPECIFIC PROCEDURES
// ============================================================================

/**
 * Job-related procedures with better-auth permissions
 */
export const jobProcedures = {
  create: protectedProcedure.use(jobPermissions.create()),
  read: protectedProcedure.use(jobPermissions.read()),
  update: protectedProcedure.use(jobPermissions.update()),
  delete: protectedProcedure.use(jobPermissions.delete()),
  list: protectedProcedure.use(jobPermissions.list()),
  publish: protectedProcedure.use(jobPermissions.publish()),
  award: protectedProcedure.use(jobPermissions.award()),
  complete: protectedProcedure.use(jobPermissions.complete()),
};

/**
 * Property-related procedures with better-auth permissions
 */
export const propertyProcedures = {
  create: protectedProcedure.use(propertyPermissions.create()),
  read: protectedProcedure.use(propertyPermissions.read()),
  update: protectedProcedure.use(propertyPermissions.update()),
  delete: protectedProcedure.use(propertyPermissions.delete()),
  list: protectedProcedure.use(propertyPermissions.list()),
};

/**
 * Bid-related procedures with better-auth permissions
 */
export const bidProcedures = {
  create: protectedProcedure.use(bidPermissions.create()),
  read: protectedProcedure.use(bidPermissions.read()),
  update: protectedProcedure.use(bidPermissions.update()),
  delete: protectedProcedure.use(bidPermissions.delete()),
  list: protectedProcedure.use(bidPermissions.list()),
  accept: protectedProcedure.use(bidPermissions.accept()),
  reject: protectedProcedure.use(bidPermissions.reject()),
  withdraw: protectedProcedure.use(bidPermissions.withdraw()),
};

/**
 * Organization-related procedures with better-auth permissions
 */
export const organizationProcedures = {
  create: protectedProcedure.use(organizationPermissions.create()),
  read: protectedProcedure.use(organizationPermissions.read()),
  update: protectedProcedure.use(organizationPermissions.update()),
  delete: protectedProcedure.use(organizationPermissions.delete()),
  list: protectedProcedure.use(organizationPermissions.list()),
  manage: protectedProcedure.use(organizationPermissions.manage()),
};

/**
 * Schedule-related procedures with better-auth permissions
 */
export const scheduleProcedures = {
  create: protectedProcedure.use(schedulePermissions.create()),
  read: protectedProcedure.use(schedulePermissions.read()),
  update: protectedProcedure.use(schedulePermissions.update()),
  delete: protectedProcedure.use(schedulePermissions.delete()),
  list: protectedProcedure.use(schedulePermissions.list()),
  confirm: protectedProcedure.use(schedulePermissions.confirm()),
  reschedule: protectedProcedure.use(schedulePermissions.reschedule()),
};

/**
 * Message-related procedures with better-auth permissions
 */
export const messageProcedures = {
  create: protectedProcedure.use(messagePermissions.create()),
  read: protectedProcedure.use(messagePermissions.read()),
  list: protectedProcedure.use(messagePermissions.list()),
};

/**
 * Review-related procedures with better-auth permissions
 */
export const reviewProcedures = {
  create: protectedProcedure.use(reviewPermissions.create()),
  read: protectedProcedure.use(reviewPermissions.read()),
  list: protectedProcedure.use(reviewPermissions.list()),
};

/**
 * Service-related procedures with better-auth permissions
 */
export const serviceProcedures = {
  create: protectedProcedure.use(servicePermissions.create()),
  read: protectedProcedure.use(servicePermissions.read()),
  update: protectedProcedure.use(servicePermissions.update()),
  delete: protectedProcedure.use(servicePermissions.delete()),
  list: protectedProcedure.use(servicePermissions.list()),
};

/**
 * Template-related procedures with better-auth permissions
 */
export const templateProcedures = {
  create: protectedProcedure.use(templatePermissions.create()),
  read: protectedProcedure.use(templatePermissions.read()),
  update: protectedProcedure.use(templatePermissions.update()),
  delete: protectedProcedure.use(templatePermissions.delete()),
  list: protectedProcedure.use(templatePermissions.list()),
};

/**
 * Trade-related procedures (admin only)
 */
export const tradeProcedures = {
  create: adminProcedure,
  read: protectedProcedure,
  update: adminProcedure,
  delete: adminProcedure,
  list: protectedProcedure,
};

// ============================================================================
// UTILITY PROCEDURE BUILDERS
// ============================================================================

/**
 * Create a procedure with specific permission requirements
 */
export function createPermissionProcedure(
  resource: string,
  action: string,
  options?: {
    requireOwnership?: boolean;
    customCheck?: (ctx: BetterAuthContext) => Promise<boolean> | boolean;
  },
) {
  return protectedProcedure.use(
    createBetterAuthPermission(resource, action, options),
  );
}

/**
 * Create a CRUD procedure set for a resource
 */
export function createCRUDProcedures(resource: string) {
  return {
    create: createPermissionProcedure(resource, "create"),
    read: createPermissionProcedure(resource, "read"),
    update: createPermissionProcedure(resource, "update", {
      requireOwnership: true,
    }),
    delete: createPermissionProcedure(resource, "delete", {
      requireOwnership: true,
    }),
    list: createPermissionProcedure(resource, "read"),
  };
}

/**
 * Create a read-only procedure set for a resource
 */
export function createReadOnlyProcedures(resource: string) {
  return {
    read: createPermissionProcedure(resource, "read"),
    list: createPermissionProcedure(resource, "read"),
  };
}

/**
 * Create an admin-only procedure set for a resource
 */
export function createAdminProcedures() {
  return {
    create: adminProcedure,
    read: adminProcedure,
    update: adminProcedure,
    delete: adminProcedure,
    list: adminProcedure,
    manage: adminProcedure,
  };
}
