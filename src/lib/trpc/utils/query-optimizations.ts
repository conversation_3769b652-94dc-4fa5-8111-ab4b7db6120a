import { and, asc, desc, eq, inArray, sql } from "drizzle-orm";
import type { PgColumn, PgTable } from "drizzle-orm/pg-core";
import type { Db } from "@/db";
import {
  account,
  address,
  bid,
  job,
  membership,
  organization,
  property,
  review,
  schedule,
  trade,
} from "@/db/schema";

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

type JobWithRelations = {
  id: string;
  name: string;
  budget: number;
  status: string;
  createdAt: Date;
  startsAt: Date;
  deadline: Date;
  completedAt: Date | null;
  updatedAt: Date;
  jobType: string;
  taskBids: boolean;
  isRecurring: boolean;
  contractorCompleted: boolean;
  homeownerCompleted: boolean;
  recurringFrequency: string | null;
  propertyId: string;
  property: {
    id: string;
    name: string;
    imageUrl: string | null;
    userId: string;
    address: {
      street: string;
      city: string;
      state: string;
      zip: string;
    };
  };
  bidsCount: number;
  avgBidAmount: number | null;
  lowestBidAmount: number | null;
  highestBidAmount: number | null;
  acceptedBidId: string | null;
  hasAcceptedBid: boolean;
};

type OrganizationWithStats = {
  id: string;
  name: string;
  description: string | null;
  logoUrl: string | null;
  email: string | null;
  phone: string | null;
  acceptsQuickHire: boolean;
  createdAt: Date;
  updatedAt: Date;
  trade: {
    id: string;
    name: string;
  } | null;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
  };
  totalBids: number;
  acceptedBids: number;
  completedJobs: number;
  avgRating: number | null;
  reviewCount: number;
  memberCount: number;
  winRate: number;
};

type PropertyWithStats = {
  id: string;
  name: string;
  imageUrl: string | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
  };
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  totalBudget: number | null;
  avgJobBudget: number | null;
  lastJobCreated: Date | null;
  lastJobCompleted: Date | null;
};

/**
 * Query Optimization Utilities
 *
 * This module provides optimized query patterns to replace inefficient queries
 * found in the tRPC routers. It focuses on:
 * 1. Eliminating N+1 queries
 * 2. Batch loading related data
 * 3. Optimizing complex joins
 * 4. Providing reusable query builders
 */

// ============================================================================
// BATCH QUERY UTILITIES
// ============================================================================

/**
 * Batch load jobs with all related data
 * Eliminates N+1 queries when loading multiple jobs
 */
export async function batchLoadJobsWithRelations(
  db: Db,
  jobIds: string[],
): Promise<Map<string, JobWithRelations>> {
  if (jobIds.length === 0) return new Map();

  const jobs = await db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      startsAt: job.startsAt,
      deadline: job.deadline,
      completedAt: job.completedAt,
      updatedAt: job.updatedAt,
      jobType: job.jobType,
      taskBids: job.taskBids,
      isRecurring: job.isRecurring,
      contractorCompleted: job.contractorCompleted,
      homeownerCompleted: job.homeownerCompleted,
      recurringFrequency: job.recurringFrequency,
      propertyId: job.propertyId,

      // Property data (flattened)
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,
      propertyUserId: property.userId,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Aggregated bid data
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
      lowestBidAmount: sql<number>`min(${bid.amount})`.as("lowestBidAmount"),
      highestBidAmount: sql<number>`max(${bid.amount})`.as("highestBidAmount"),
      acceptedBidId: sql<string>`
        max(${bid.id}) filter (where ${bid.status} = 'ACCEPTED')
      `.as("acceptedBidId"),
      hasAcceptedBid: sql<boolean>`bool_or(${bid.status} = 'ACCEPTED')`.as(
        "hasAcceptedBid",
      ),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(inArray(job.id, jobIds))
    .groupBy(job.id, property.id, address.id);

  // Convert to Map for O(1) lookup
  const jobMap = new Map();
  for (const jobData of jobs) {
    jobMap.set(jobData.id, {
      ...jobData,
      property: {
        id: jobData.propertyId,
        name: jobData.propertyName,
        imageUrl: jobData.propertyImageUrl,
        userId: jobData.propertyUserId,
        address: {
          street: jobData.addressStreet,
          city: jobData.addressCity,
          state: jobData.addressState,
          zip: jobData.addressZip,
        },
      },
    });
  }

  return jobMap;
}

/**
 * Batch load organizations with statistics
 * Eliminates N+1 queries when loading multiple organizations
 */
export async function batchLoadOrganizationsWithStats(
  db: Db,
  organizationIds: string[],
): Promise<Map<string, OrganizationWithStats>> {
  if (organizationIds.length === 0) return new Map();

  const organizations = await db
    .select({
      // Organization data
      id: organization.id,
      name: organization.name,
      description: organization.description,
      logoUrl: organization.logoUrl,
      email: organization.email,
      phone: organization.phone,
      acceptsQuickHire: organization.acceptsQuickHire,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,

      // Trade data
      tradeId: trade.id,
      tradeName: trade.name,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      memberCount: sql<number>`count(distinct ${membership.userId})`.as(
        "memberCount",
      ),

      // Performance metrics
      winRate: sql<number>`
        case
          when count(distinct ${bid.id}) > 0
          then round((count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')::decimal / count(distinct ${bid.id})) * 100, 2)
          else 0
        end
      `.as("winRate"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, and(eq(bid.jobId, job.id), eq(bid.status, "ACCEPTED")))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .where(inArray(organization.id, organizationIds))
    .groupBy(organization.id, trade.id, address.id);

  // Convert to Map for O(1) lookup
  const orgMap = new Map();
  for (const orgData of organizations) {
    orgMap.set(orgData.id, {
      ...orgData,
      trade: orgData.tradeId
        ? {
            id: orgData.tradeId,
            name: orgData.tradeName,
          }
        : null,
      address: {
        street: orgData.addressStreet,
        city: orgData.addressCity,
        state: orgData.addressState,
        zip: orgData.addressZip,
      },
    });
  }

  return orgMap;
}

/**
 * Batch load properties with job statistics
 * Eliminates N+1 queries when loading multiple properties
 */
export async function batchLoadPropertiesWithStats(
  db: Db,
  propertyIds: string[],
): Promise<Map<string, PropertyWithStats>> {
  if (propertyIds.length === 0) return new Map();

  const properties = await db
    .select({
      // Property data
      id: property.id,
      name: property.name,
      imageUrl: property.imageUrl,
      userId: property.userId,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      totalBudget: sql<number>`sum(${job.budget})`.as("totalBudget"),
      avgJobBudget: sql<number>`avg(${job.budget})`.as("avgJobBudget"),

      // Latest activity
      lastJobCreated: sql<Date>`max(${job.createdAt})`.as("lastJobCreated"),
      lastJobCompleted: sql<Date>`max(${job.completedAt})`.as(
        "lastJobCompleted",
      ),
    })
    .from(property)
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(job, eq(property.id, job.propertyId))
    .where(inArray(property.id, propertyIds))
    .groupBy(property.id, address.id);

  // Convert to Map for O(1) lookup
  const propertyMap = new Map();
  for (const propData of properties) {
    propertyMap.set(propData.id, {
      ...propData,
      address: {
        street: propData.addressStreet,
        city: propData.addressCity,
        state: propData.addressState,
        zip: propData.addressZip,
      },
    });
  }

  return propertyMap;
}

// ============================================================================
// OPTIMIZED QUERY BUILDERS
// ============================================================================

/**
 * Optimized query for user's jobs with property and bid information
 * Replaces inefficient subqueries in jobs/listing.ts
 */
export function buildUserJobsQuery(db: Db, userId: string) {
  return db
    .select({
      job,
      property,
      address,
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      acceptedBid: sql<any>`
        json_agg(
          json_build_object(
            'id', ${bid.id},
            'amount', ${bid.amount},
            'status', ${bid.status},
            'organization', json_build_object(
              'id', ${organization.id},
              'name', ${organization.name}
            )
          )
        ) FILTER (WHERE ${bid.status} = 'ACCEPTED')
      `.as("acceptedBid"),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(organization, eq(bid.organizationId, organization.id))
    .where(eq(property.userId, userId))
    .groupBy(job.id, property.id, address.id)
    .orderBy(desc(job.createdAt));
}

/**
 * Optimized query for organization's active jobs
 * Eliminates multiple queries in contractor stats
 */
export function buildOrganizationActiveJobsQuery(
  db: Db,
  organizationId: string,
) {
  return db
    .select({
      job,
      property,
      address,
      bid,
      scheduleInfo: sql<any>`
        json_agg(
          json_build_object(
            'id', ${schedule.id},
            'proposedStartDate', ${schedule.proposedStartDate},
            'proposedEndDate', ${schedule.proposedEndDate},
            'status', ${schedule.status}
          )
        ) FILTER (WHERE ${schedule.id} IS NOT NULL)
      `.as("scheduleInfo"),
    })
    .from(job)
    .innerJoin(
      bid,
      and(
        eq(job.id, bid.jobId),
        eq(bid.organizationId, organizationId),
        eq(bid.status, "ACCEPTED"),
      ),
    )
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(schedule, eq(job.id, schedule.jobId))
    .where(eq(job.status, "PUBLISHED"))
    .groupBy(job.id, property.id, address.id, bid.id)
    .orderBy(desc(job.createdAt));
}

// ============================================================================
// SEARCH OPTIMIZATION
// ============================================================================

/**
 * Optimized contractor search with full-text search capabilities
 * Replaces ILIKE queries with proper text search
 */
export function buildContractorSearchQuery(
  db: Db,
  searchQuery: string,
  excludeIds: string[] = [],
  limit = 10,
) {
  const searchConditions = [];

  // Use proper text search instead of ILIKE for better performance
  if (searchQuery.trim()) {
    const tsQuery = searchQuery.trim().split(" ").join(" & ");
    searchConditions.push(
      sql`(
        to_tsvector('english', ${organization.name}) @@ to_tsquery('english', ${tsQuery}) OR
        to_tsvector('english', ${trade.name}) @@ to_tsquery('english', ${tsQuery}) OR
        to_tsvector('english', coalesce(${organization.description}, '')) @@ to_tsquery('english', ${tsQuery})
      )`,
    );
  }

  if (excludeIds.length > 0) {
    searchConditions.push(
      sql`${organization.id} NOT IN (${sql.join(
        excludeIds.map((id) => sql`${id}`),
        sql`, `,
      )})`,
    );
  }

  return db
    .select({
      organization,
      trade,
      address,
      memberCount: sql<number>`count(${membership.userId})`.as("memberCount"),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(${review.id})`.as("reviewCount"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, and(eq(bid.jobId, job.id), eq(job.status, "COMPLETED")))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(searchConditions.length > 0 ? and(...searchConditions) : undefined)
    .groupBy(organization.id, trade.id, address.id)
    .orderBy(
      desc(sql`avg(${review.rating})`),
      desc(sql`count(${review.id})`),
      asc(organization.name),
    )
    .limit(limit);
}

// ============================================================================
// STATISTICS OPTIMIZATION
// ============================================================================

/**
 * Optimized admin statistics query
 * Combines multiple count queries into a single query
 */
export async function getOptimizedAdminStats(db: Db) {
  const [stats] = await db
    .select({
      totalUsers: sql<number>`count(distinct ${account.id})`.as("totalUsers"),
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      publishedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "publishedJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} in ('AWARDED', 'CLOSED'))`.as(
          "completedJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      totalOrganizations: sql<number>`count(distinct ${organization.id})`.as(
        "totalOrganizations",
      ),
    })
    .from(account)
    .fullJoin(job, sql`true`)
    .fullJoin(bid, sql`true`)
    .fullJoin(organization, sql`true`);

  return stats;
}

/**
 * Optimized user statistics query
 * Replaces multiple separate queries in accounts router
 */
export async function getOptimizedUserStats(db: Db, userId: string) {
  const [stats] = await db
    .select({
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} in ('AWARDED', 'CLOSED'))`.as(
          "completedJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      avgJobBudget: sql<number>`avg(${job.budget})`.as("avgJobBudget"),
    })
    .from(property)
    .leftJoin(job, eq(property.id, job.propertyId))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(property.userId, userId));

  return stats;
}

// ============================================================================
// PAGINATION UTILITIES
// ============================================================================

/**
 * Cursor-based pagination builder for large datasets
 */
export function buildCursorPaginationQuery<T extends PgTable>(
  baseQuery: any,
  cursorColumn: PgColumn,
  cursor?: string | Date,
  limit = 20,
  direction: "asc" | "desc" = "desc",
) {
  let query = baseQuery;

  if (cursor) {
    const operator = direction === "desc" ? sql`<` : sql`>`;
    query = query.where(sql`${cursorColumn} ${operator} ${cursor}`);
  }

  query = query
    .orderBy(direction === "desc" ? desc(cursorColumn) : asc(cursorColumn))
    .limit(limit + 1); // +1 to check if there's a next page

  return query;
}
