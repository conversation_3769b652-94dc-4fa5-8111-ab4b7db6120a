import { and, eq } from "drizzle-orm";
import JobCompletionCongratulationsEmail from "@/components/emails/job-completion-congratulations";
import { db } from "@/db";
import { bid, job, membership, user } from "@/db/schema";
import { sendEmail } from "@/lib/email";

export async function sendJobCompletionCongratulations(jobId: string) {
  // Get the job details
  const jobData = await db.query.job.findFirst({
    where: eq(job.id, jobId),
    with: {
      property: true,
      bids: {
        where: eq(bid.status, "ACCEPTED"),
        with: {
          organization: true,
        },
      },
    },
  });

  if (!jobData) return;

  // Find property owner
  const propertyOwner = await db.query.user.findFirst({
    where: eq(user.id, jobData.property.userId),
  });

  if (!propertyOwner) return;

  // For jobs with accepted bids
  let contractorUserId: string | undefined;
  let contractorOrgName = "Contractor";

  if (jobData.bids.length > 0) {
    // Get the contractor from the accepted bid
    const acceptedBid = jobData.bids[0];
    contractorOrgName = acceptedBid?.organization.name || "Contractor";

    if (!acceptedBid) return;

    // Find the contractor user
    const contractorMembership = await db.query.membership.findFirst({
      where: and(
        eq(membership.organizationId, acceptedBid.organizationId),
        eq(membership.role, "OWNER"),
      ),
      columns: {
        userId: true,
      },
    });

    if (contractorMembership) {
      contractorUserId = contractorMembership.userId;
    }
  }

  // Send email to homeowner
  if (propertyOwner.email) {
    await sendEmail({
      to: propertyOwner.email,
      subject: "Jack here - congratulations on completing your project! 🎉",
      template: JobCompletionCongratulationsEmail({
        jobName: jobData.name,
        jobId: jobData.id,
        recipientRole: "HOMEOWNER",
        counterpartyName: contractorOrgName,
        userId: propertyOwner.id,
        recipientEmail: propertyOwner.email,
      }),
    });
  }

  // Send email to contractor if available
  if (contractorUserId) {
    const contractor = await db.query.user.findFirst({
      where: eq(user.id, contractorUserId),
    });

    if (contractor?.email) {
      await sendEmail({
        to: contractor.email,
        subject: "Jack here - congratulations on completing the project! 🎉",
        template: JobCompletionCongratulationsEmail({
          jobName: jobData.name,
          jobId: jobData.id,
          recipientRole: "PROFESSIONAL",
          counterpartyName: propertyOwner.name,
          userId: contractorUserId,
          recipientEmail: contractor.email,
        }),
      });
    }
  }
}
