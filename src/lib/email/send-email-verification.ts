import EmailVerificationEmail from "@/components/emails/email-verification";
import { sendEmail } from "@/lib/email";

export async function sendEmailVerification({
  email,
  userId,
  verificationCode,
  verificationLink,
}: {
  email: string;
  userId?: string;
  verificationCode: string;
  verificationLink: string;
}) {
  try {
    // Send the verification email
    await sendEmail({
      to: email,
      subject: "Hey there! Jack here - let's verify your email 👋",
      template: EmailVerificationEmail({
        verificationCode,
        verificationLink,
        userId,
        recipientEmail: email,
      }),
    });

    return { success: true };
  } catch (error) {
    console.error("Error sending verification email:", error);
    return { success: false, error };
  }
}
